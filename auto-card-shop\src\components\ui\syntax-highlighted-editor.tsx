'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Button } from './button'
import { Markdown } from './markdown'
import { cn } from '@/lib/utils'
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Link as LinkIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Eye,
  EyeOff,
  Copy,
  Undo,
  Redo,
  Type,
  Palette,
  Settings
} from 'lucide-react'

interface SyntaxHighlightedEditorProps {
  content: string
  onChange: (content: string) => void
  placeholder?: string
  className?: string
  showPreview?: boolean
  enableToolbar?: boolean
  enableSyntaxHighlight?: boolean
}

export function SyntaxHighlightedEditor({
  content,
  onChange,
  placeholder = '请输入内容...',
  className,
  showPreview = true,
  enableToolbar = true,
  enableSyntaxHighlight = true
}: SyntaxHighlightedEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [showSyntaxHighlight, setShowSyntaxHighlight] = useState(enableSyntaxHighlight)
  const [history, setHistory] = useState<string[]>([content])
  const [historyIndex, setHistoryIndex] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const highlightRef = useRef<HTMLDivElement>(null)

  // 同步滚动
  const handleScroll = (e: React.UIEvent<HTMLTextAreaElement>) => {
    if (highlightRef.current) {
      highlightRef.current.scrollTop = e.currentTarget.scrollTop
      highlightRef.current.scrollLeft = e.currentTarget.scrollLeft
    }
  }

  // 添加到历史记录
  const addToHistory = (newContent: string) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(newContent)
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  // 撤销
  const undo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      setHistoryIndex(newIndex)
      onChange(history[newIndex])
    }
  }

  // 重做
  const redo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      setHistoryIndex(newIndex)
      onChange(history[newIndex])
    }
  }

  // 插入文本
  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = content.substring(start, end)
    const textToInsert = selectedText || placeholder
    
    const newContent = 
      content.substring(0, start) + 
      before + textToInsert + after + 
      content.substring(end)
    
    onChange(newContent)
    addToHistory(newContent)

    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus()
      const newCursorPos = start + before.length + textToInsert.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    }, 0)
  }

  // 工具栏按钮
  const toolbarButtons = [
    {
      icon: Heading1,
      title: '标题 1',
      action: () => insertText('# ', '', '标题')
    },
    {
      icon: Heading2,
      title: '标题 2',
      action: () => insertText('## ', '', '标题')
    },
    {
      icon: Heading3,
      title: '标题 3',
      action: () => insertText('### ', '', '标题')
    },
    { divider: true },
    {
      icon: Bold,
      title: '粗体',
      action: () => insertText('**', '**', '粗体文字')
    },
    {
      icon: Italic,
      title: '斜体',
      action: () => insertText('*', '*', '斜体文字')
    },
    {
      icon: Code,
      title: '代码',
      action: () => insertText('`', '`', '代码')
    },
    { divider: true },
    {
      icon: List,
      title: '无序列表',
      action: () => insertText('- ', '', '列表项')
    },
    {
      icon: ListOrdered,
      title: '有序列表',
      action: () => insertText('1. ', '', '列表项')
    },
    {
      icon: Quote,
      title: '引用',
      action: () => insertText('> ', '', '引用内容')
    },
    { divider: true },
    {
      icon: LinkIcon,
      title: '链接',
      action: () => insertText('[', '](https://example.com)', '链接文字')
    },
    {
      icon: ImageIcon,
      title: '图片',
      action: () => insertText('![', '](https://example.com/image.jpg)', '图片描述')
    },
    {
      icon: TableIcon,
      title: '表格',
      action: () => insertText('| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容1 | 内容2 | 内容3 |\n', '', '')
    }
  ]

  const handleContentChange = (newContent: string) => {
    onChange(newContent)
  }

  const copyContent = () => {
    navigator.clipboard.writeText(content)
  }

  // 处理Tab键缩进
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault()
      const textarea = e.currentTarget
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      
      const newContent = content.substring(0, start) + '  ' + content.substring(end)
      onChange(newContent)
      
      setTimeout(() => {
        textarea.setSelectionRange(start + 2, start + 2)
      }, 0)
    }
  }

  return (
    <div className={cn('border border-gray-300 rounded-lg overflow-hidden', className)}>
      {/* 工具栏 */}
      {enableToolbar && (
        <div className="border-b border-gray-200 bg-gray-50 p-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 flex-wrap">
              {/* 撤销重做 */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={undo}
                disabled={historyIndex <= 0}
                title="撤销"
              >
                <Undo className="w-4 h-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={redo}
                disabled={historyIndex >= history.length - 1}
                title="重做"
              >
                <Redo className="w-4 h-4" />
              </Button>
              
              <div className="w-px h-6 bg-gray-300 mx-1" />

              {/* Markdown工具 */}
              {toolbarButtons.map((button, index) => {
                if (button.divider) {
                  return <div key={index} className="w-px h-6 bg-gray-300 mx-1" />
                }
                
                const Icon = button.icon!
                return (
                  <Button
                    key={index}
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={button.action}
                    title={button.title}
                  >
                    <Icon className="w-4 h-4" />
                  </Button>
                )
              })}
            </div>

            <div className="flex items-center gap-2">
              {/* 语法高亮切换 */}
              {enableSyntaxHighlight && !isPreviewMode && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSyntaxHighlight(!showSyntaxHighlight)}
                  title={showSyntaxHighlight ? '关闭语法高亮' : '开启语法高亮'}
                >
                  <Palette className={cn('w-4 h-4', showSyntaxHighlight ? 'text-blue-600' : 'text-gray-400')} />
                </Button>
              )}

              {/* 复制按钮 */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={copyContent}
                title="复制内容"
              >
                <Copy className="w-4 h-4" />
              </Button>

              {/* 预览切换 */}
              {showPreview && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPreviewMode(!isPreviewMode)}
                  title={isPreviewMode ? '编辑模式' : '预览模式'}
                >
                  {isPreviewMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  <span className="ml-1 text-xs">
                    {isPreviewMode ? '编辑' : '预览'}
                  </span>
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 编辑器内容 */}
      <div className="relative">
        {isPreviewMode ? (
          <div className="min-h-[300px] max-h-[500px] overflow-y-auto p-4 bg-white">
            {content ? (
              <Markdown content={content} variant="default" />
            ) : (
              <p className="text-gray-500 italic">暂无内容</p>
            )}
          </div>
        ) : (
          <div className="relative">
            {/* 语法高亮背景 */}
            {showSyntaxHighlight && (
              <div
                ref={highlightRef}
                className="absolute inset-0 overflow-hidden pointer-events-none"
                style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
              >
                <SyntaxHighlighter
                  language="markdown"
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    padding: '16px',
                    background: 'transparent',
                    fontSize: '14px',
                    lineHeight: '1.5',
                    minHeight: '300px',
                    maxHeight: '500px'
                  }}
                  showLineNumbers={false}
                >
                  {content || ' '}
                </SyntaxHighlighter>
              </div>
            )}
            
            {/* 文本输入框 */}
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              onScroll={handleScroll}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className={cn(
                "w-full min-h-[300px] max-h-[500px] p-4 border-0 resize-none focus:outline-none text-sm leading-relaxed",
                showSyntaxHighlight ? "bg-transparent text-transparent caret-gray-900" : "bg-white text-gray-900"
              )}
              style={{ 
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                position: 'relative',
                zIndex: showSyntaxHighlight ? 2 : 1
              }}
              spellCheck={false}
            />
          </div>
        )}
      </div>

      {/* 状态栏 */}
      <div className="border-t border-gray-200 bg-gray-50 px-4 py-2 text-xs text-gray-500 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <span>字符数: {content.length}</span>
          <span>行数: {content.split('\n').length}</span>
          {showSyntaxHighlight && !isPreviewMode && (
            <span className="flex items-center gap-1">
              <Palette className="w-3 h-3 text-blue-600" />
              语法高亮
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Type className="w-3 h-3" />
          <span>Markdown</span>
        </div>
      </div>
    </div>
  )
}
