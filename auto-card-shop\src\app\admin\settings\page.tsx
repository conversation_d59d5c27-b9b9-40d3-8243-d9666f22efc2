'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Save, Settings, AlertTriangle, CheckCircle } from 'lucide-react'

export default function SystemSettings() {
  const [loading, setLoading] = useState(false)
  const [maintenanceMode, setMaintenanceMode] = useState(false)
  const [maintenanceMessage, setMaintenanceMessage] = useState('网站正在维护中，请稍后再试。')

  useEffect(() => {
    fetchMaintenanceSettings()
  }, [])

  const fetchMaintenanceSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings/maintenance')
      if (response.ok) {
        const data = await response.json()
        setMaintenanceMode(data.maintenanceMode || false)
        setMaintenanceMessage(data.maintenanceMessage || '网站正在维护中，请稍后再试。')
      }
    } catch (error) {
      console.error('获取维护设置失败:', error)
    }
  }

  const handleSaveMaintenanceSettings = async () => {
    setLoading(true)

    try {
      const response = await fetch('/api/admin/settings/maintenance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          maintenanceMode,
          maintenanceMessage
        }),
      })

      if (response.ok) {
        alert('维护设置保存成功')
      } else {
        alert('保存失败')
      }
    } catch (error) {
      alert('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
        <p className="text-gray-600">管理网站维护模式</p>
      </div>

      {/* 维护模式设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <Settings className="w-6 h-6 text-blue-600 mr-3" />
          <h2 className="text-lg font-semibold text-gray-900">维护模式</h2>
        </div>

        {/* 当前状态 */}
        <div className="mb-6 p-4 rounded-lg border-2 border-dashed">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {maintenanceMode ? (
                <AlertTriangle className="w-5 h-5 text-orange-500 mr-2" />
              ) : (
                <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              )}
              <span className="font-medium">
                当前状态: {maintenanceMode ? '维护模式已启用' : '网站正常运行'}
              </span>
            </div>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              maintenanceMode
                ? 'bg-orange-100 text-orange-800'
                : 'bg-green-100 text-green-800'
            }`}>
              {maintenanceMode ? '维护中' : '正常'}
            </div>
          </div>
          {maintenanceMode && (
            <p className="mt-2 text-sm text-gray-600">
              网站当前处于维护模式，访客将看到维护页面
            </p>
          )}
        </div>

        <div className="space-y-6">
          {/* 维护模式开关 */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={maintenanceMode}
                onChange={(e) => setMaintenanceMode(e.target.checked)}
                className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-3 text-sm font-medium text-gray-900">
                启用维护模式
              </span>
            </label>
            <p className="mt-2 text-sm text-gray-500">
              启用后，网站将显示维护页面，只有管理员可以正常访问
            </p>
          </div>

          {/* 维护消息设置 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              维护页面消息
            </label>
            <textarea
              rows={4}
              value={maintenanceMessage}
              onChange={(e) => setMaintenanceMessage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入维护页面显示的消息..."
            />
            <p className="mt-1 text-sm text-gray-500">
              此消息将显示在维护页面上，告知访客网站维护的原因
            </p>
          </div>

          {/* 维护模式说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">维护模式说明</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 启用维护模式后，普通用户将无法访问网站</li>
              <li>• 管理员仍可正常登录和管理网站</li>
              <li>• 维护页面将显示自定义消息</li>
              <li>• 建议在系统更新或重要维护时启用</li>
            </ul>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end">
            <Button
              onClick={handleSaveMaintenanceSettings}
              disabled={loading}
              className="px-6"
            >
              <Save className="w-4 h-4 mr-2" />
              {loading ? '保存中...' : '保存设置'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

          {/* 支付设置 */}
          {activeTab === 'payment' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900">支付设置</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Stripe 公钥
                  </label>
                  <input
                    type="text"
                    value={paymentSettings.stripePublishableKey}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, stripePublishableKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="pk_test_..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Stripe 私钥
                  </label>
                  <input
                    type="password"
                    value={paymentSettings.stripeSecretKey}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, stripeSecretKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="sk_test_..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Webhook 密钥
                  </label>
                  <input
                    type="password"
                    value={paymentSettings.stripeWebhookSecret}
                    onChange={(e) => setPaymentSettings({ ...paymentSettings, stripeWebhookSecret: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="whsec_..."
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      货币
                    </label>
                    <select
                      value={paymentSettings.currency}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, currency: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="USD">美元 (USD)</option>
                      <option value="EUR">欧元 (EUR)</option>
                      <option value="CNY">人民币 (CNY)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      税率 (%)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={paymentSettings.taxRate}
                      onChange={(e) => setPaymentSettings({ ...paymentSettings, taxRate: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={() => handleSaveSettings('payment', paymentSettings)}
                disabled={loading}
              >
                <Save className="w-4 h-4 mr-2" />
                保存设置
              </Button>
            </div>
          )}

          {/* 邮件设置 */}
          {activeTab === 'email' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900">邮件设置</h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SMTP 主机
                    </label>
                    <input
                      type="text"
                      value={emailSettings.smtpHost}
                      onChange={(e) => setEmailSettings({ ...emailSettings, smtpHost: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="smtp.gmail.com"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SMTP 端口
                    </label>
                    <input
                      type="number"
                      value={emailSettings.smtpPort}
                      onChange={(e) => setEmailSettings({ ...emailSettings, smtpPort: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SMTP 用户名
                    </label>
                    <input
                      type="text"
                      value={emailSettings.smtpUser}
                      onChange={(e) => setEmailSettings({ ...emailSettings, smtpUser: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      SMTP 密码
                    </label>
                    <input
                      type="password"
                      value={emailSettings.smtpPassword}
                      onChange={(e) => setEmailSettings({ ...emailSettings, smtpPassword: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      发件人邮箱
                    </label>
                    <input
                      type="email"
                      value={emailSettings.fromEmail}
                      onChange={(e) => setEmailSettings({ ...emailSettings, fromEmail: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      发件人名称
                    </label>
                    <input
                      type="text"
                      value={emailSettings.fromName}
                      onChange={(e) => setEmailSettings({ ...emailSettings, fromName: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={() => handleSaveSettings('email', emailSettings)}
                disabled={loading}
              >
                <Save className="w-4 h-4 mr-2" />
                保存设置
              </Button>
            </div>
          )}

          {/* 安全设置 */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900">安全设置</h2>
              
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-yellow-800">安全建议</h3>
                  <ul className="mt-2 text-sm text-yellow-700 space-y-1">
                    <li>• 定期更换管理员密码</li>
                    <li>• 使用强密码（至少8位，包含大小写字母、数字和特殊字符）</li>
                    <li>• 定期备份数据库</li>
                    <li>• 监控异常登录活动</li>
                    <li>• 及时更新系统依赖</li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-900">密码策略</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                      <span className="ml-2 text-sm text-gray-700">要求强密码（至少8位）</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                      <span className="ml-2 text-sm text-gray-700">启用登录失败锁定</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                      <span className="ml-2 text-sm text-gray-700">记录登录日志</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 数据库设置 */}
          {activeTab === 'database' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900">数据库管理</h2>
              
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-blue-800">数据库信息</h3>
                  <div className="mt-2 text-sm text-blue-700 space-y-1">
                    <div>类型: SQLite</div>
                    <div>位置: ./prisma/dev.db</div>
                    <div>状态: 正常运行</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-900">数据库操作</h3>
                  <div className="space-y-2">
                    <Button variant="outline">
                      <Database className="w-4 h-4 mr-2" />
                      备份数据库
                    </Button>
                    <Button variant="outline">
                      <Database className="w-4 h-4 mr-2" />
                      恢复数据库
                    </Button>
                    <Button variant="outline">
                      <Database className="w-4 h-4 mr-2" />
                      清理过期数据
                    </Button>
                  </div>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-red-800">危险操作</h3>
                  <p className="mt-1 text-sm text-red-700">
                    以下操作将永久删除数据，请谨慎操作
                  </p>
                  <div className="mt-3">
                    <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50">
                      重置数据库
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
