(function(){function f(){console.log(e.apply(null,arguments))}function e(h){for(var e=1,d=[].slice.call(arguments),g=0,f=h.length,a="",b,i=!1,j,c=function(){return d[e++]},k=function(){for(var a="";h[g].match(/\d/);)a+=h[g++];return a.length>0?parseInt(a):null};g<f;++g)if(b=h[g],i)switch(i=!1,j=k(),b){case "b":a+=parseInt(c(),10).toString(2);break;case "c":b=c();a+=typeof b==="string"||b instanceof String?b:String.fromCharCode(parseInt(b,10));break;case "d":a+=parseInt(c(),10);break;case "f":a+=parseFloat(c()).toFixed(j||
6);break;case "o":a+="0"+parseInt(c(),10).toString(8);break;case "s":a+=c();break;case "x":a+="0x"+parseInt(c(),10).toString(16);break;case "X":a+="0x"+parseInt(c(),10).toString(16).toUpperCase();break;default:a+=b}else b==="%"?i=!0:a+=b;return a}var d;d=typeof module!=="undefined"?module.exports=e:function(){return this||(0,eval)("this")}();d.format=e;d.vsprintf=function(d,f){return e.apply(null,[d].concat(f))};if(typeof console!=="undefined"&&typeof console.log==="function")d.printf=f})();
