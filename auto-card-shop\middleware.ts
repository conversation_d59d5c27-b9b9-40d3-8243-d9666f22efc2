import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { isMaintenanceMode } from '@/lib/maintenance'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查维护模式
  const maintenanceMode = await isMaintenanceMode()
  
  if (maintenanceMode) {
    // 允许访问的路径（管理员相关）
    const allowedPaths = [
      '/maintenance',
      '/auth/signin',
      '/api/auth',
      '/admin',
      '/api/admin'
    ]

    // 检查是否是允许的路径
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path))
    
    if (!isAllowedPath) {
      // 检查用户是否是管理员
      const token = await getToken({ req: request })
      
      if (!token || token.role !== 'ADMIN') {
        // 重定向到维护页面
        return NextResponse.redirect(new URL('/maintenance', request.url))
      }
    }
  }

  // 保护管理员路由
  if (pathname.startsWith('/admin')) {
    const token = await getToken({ req: request })
    
    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }
    
    if (token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
