import createLanguageAsyncLoader from "./create-language-async-loader"
export default {
  oneC: createLanguageAsyncLoader("oneC", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_oneC" */ "highlight.js/lib/languages/1c")),
  abnf: createLanguageAsyncLoader("abnf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_abnf" */ "highlight.js/lib/languages/abnf")),
  accesslog: createLanguageAsyncLoader("accesslog", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_accesslog" */ "highlight.js/lib/languages/accesslog")),
  actionscript: createLanguageAsyncLoader("actionscript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_actionscript" */ "highlight.js/lib/languages/actionscript")),
  ada: createLanguageAsyncLoader("ada", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ada" */ "highlight.js/lib/languages/ada")),
  angelscript: createLanguageAsyncLoader("angelscript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_angelscript" */ "highlight.js/lib/languages/angelscript")),
  apache: createLanguageAsyncLoader("apache", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_apache" */ "highlight.js/lib/languages/apache")),
  applescript: createLanguageAsyncLoader("applescript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_applescript" */ "highlight.js/lib/languages/applescript")),
  arcade: createLanguageAsyncLoader("arcade", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_arcade" */ "highlight.js/lib/languages/arcade")),
  arduino: createLanguageAsyncLoader("arduino", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_arduino" */ "highlight.js/lib/languages/arduino")),
  armasm: createLanguageAsyncLoader("armasm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_armasm" */ "highlight.js/lib/languages/armasm")),
  asciidoc: createLanguageAsyncLoader("asciidoc", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_asciidoc" */ "highlight.js/lib/languages/asciidoc")),
  aspectj: createLanguageAsyncLoader("aspectj", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_aspectj" */ "highlight.js/lib/languages/aspectj")),
  autohotkey: createLanguageAsyncLoader("autohotkey", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_autohotkey" */ "highlight.js/lib/languages/autohotkey")),
  autoit: createLanguageAsyncLoader("autoit", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_autoit" */ "highlight.js/lib/languages/autoit")),
  avrasm: createLanguageAsyncLoader("avrasm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_avrasm" */ "highlight.js/lib/languages/avrasm")),
  awk: createLanguageAsyncLoader("awk", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_awk" */ "highlight.js/lib/languages/awk")),
  axapta: createLanguageAsyncLoader("axapta", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_axapta" */ "highlight.js/lib/languages/axapta")),
  bash: createLanguageAsyncLoader("bash", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_bash" */ "highlight.js/lib/languages/bash")),
  basic: createLanguageAsyncLoader("basic", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_basic" */ "highlight.js/lib/languages/basic")),
  bnf: createLanguageAsyncLoader("bnf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_bnf" */ "highlight.js/lib/languages/bnf")),
  brainfuck: createLanguageAsyncLoader("brainfuck", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_brainfuck" */ "highlight.js/lib/languages/brainfuck")),
  cLike: createLanguageAsyncLoader("cLike", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_cLike" */ "highlight.js/lib/languages/c-like")),
  c: createLanguageAsyncLoader("c", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_c" */ "highlight.js/lib/languages/c")),
  cal: createLanguageAsyncLoader("cal", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_cal" */ "highlight.js/lib/languages/cal")),
  capnproto: createLanguageAsyncLoader("capnproto", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_capnproto" */ "highlight.js/lib/languages/capnproto")),
  ceylon: createLanguageAsyncLoader("ceylon", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ceylon" */ "highlight.js/lib/languages/ceylon")),
  clean: createLanguageAsyncLoader("clean", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_clean" */ "highlight.js/lib/languages/clean")),
  clojureRepl: createLanguageAsyncLoader("clojureRepl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_clojureRepl" */ "highlight.js/lib/languages/clojure-repl")),
  clojure: createLanguageAsyncLoader("clojure", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_clojure" */ "highlight.js/lib/languages/clojure")),
  cmake: createLanguageAsyncLoader("cmake", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_cmake" */ "highlight.js/lib/languages/cmake")),
  coffeescript: createLanguageAsyncLoader("coffeescript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_coffeescript" */ "highlight.js/lib/languages/coffeescript")),
  coq: createLanguageAsyncLoader("coq", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_coq" */ "highlight.js/lib/languages/coq")),
  cos: createLanguageAsyncLoader("cos", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_cos" */ "highlight.js/lib/languages/cos")),
  cpp: createLanguageAsyncLoader("cpp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_cpp" */ "highlight.js/lib/languages/cpp")),
  crmsh: createLanguageAsyncLoader("crmsh", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_crmsh" */ "highlight.js/lib/languages/crmsh")),
  crystal: createLanguageAsyncLoader("crystal", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_crystal" */ "highlight.js/lib/languages/crystal")),
  csharp: createLanguageAsyncLoader("csharp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_csharp" */ "highlight.js/lib/languages/csharp")),
  csp: createLanguageAsyncLoader("csp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_csp" */ "highlight.js/lib/languages/csp")),
  css: createLanguageAsyncLoader("css", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_css" */ "highlight.js/lib/languages/css")),
  d: createLanguageAsyncLoader("d", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_d" */ "highlight.js/lib/languages/d")),
  dart: createLanguageAsyncLoader("dart", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dart" */ "highlight.js/lib/languages/dart")),
  delphi: createLanguageAsyncLoader("delphi", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_delphi" */ "highlight.js/lib/languages/delphi")),
  diff: createLanguageAsyncLoader("diff", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_diff" */ "highlight.js/lib/languages/diff")),
  django: createLanguageAsyncLoader("django", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_django" */ "highlight.js/lib/languages/django")),
  dns: createLanguageAsyncLoader("dns", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dns" */ "highlight.js/lib/languages/dns")),
  dockerfile: createLanguageAsyncLoader("dockerfile", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dockerfile" */ "highlight.js/lib/languages/dockerfile")),
  dos: createLanguageAsyncLoader("dos", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dos" */ "highlight.js/lib/languages/dos")),
  dsconfig: createLanguageAsyncLoader("dsconfig", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dsconfig" */ "highlight.js/lib/languages/dsconfig")),
  dts: createLanguageAsyncLoader("dts", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dts" */ "highlight.js/lib/languages/dts")),
  dust: createLanguageAsyncLoader("dust", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_dust" */ "highlight.js/lib/languages/dust")),
  ebnf: createLanguageAsyncLoader("ebnf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ebnf" */ "highlight.js/lib/languages/ebnf")),
  elixir: createLanguageAsyncLoader("elixir", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_elixir" */ "highlight.js/lib/languages/elixir")),
  elm: createLanguageAsyncLoader("elm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_elm" */ "highlight.js/lib/languages/elm")),
  erb: createLanguageAsyncLoader("erb", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_erb" */ "highlight.js/lib/languages/erb")),
  erlangRepl: createLanguageAsyncLoader("erlangRepl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_erlangRepl" */ "highlight.js/lib/languages/erlang-repl")),
  erlang: createLanguageAsyncLoader("erlang", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_erlang" */ "highlight.js/lib/languages/erlang")),
  excel: createLanguageAsyncLoader("excel", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_excel" */ "highlight.js/lib/languages/excel")),
  fix: createLanguageAsyncLoader("fix", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_fix" */ "highlight.js/lib/languages/fix")),
  flix: createLanguageAsyncLoader("flix", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_flix" */ "highlight.js/lib/languages/flix")),
  fortran: createLanguageAsyncLoader("fortran", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_fortran" */ "highlight.js/lib/languages/fortran")),
  fsharp: createLanguageAsyncLoader("fsharp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_fsharp" */ "highlight.js/lib/languages/fsharp")),
  gams: createLanguageAsyncLoader("gams", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gams" */ "highlight.js/lib/languages/gams")),
  gauss: createLanguageAsyncLoader("gauss", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gauss" */ "highlight.js/lib/languages/gauss")),
  gcode: createLanguageAsyncLoader("gcode", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gcode" */ "highlight.js/lib/languages/gcode")),
  gherkin: createLanguageAsyncLoader("gherkin", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gherkin" */ "highlight.js/lib/languages/gherkin")),
  glsl: createLanguageAsyncLoader("glsl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_glsl" */ "highlight.js/lib/languages/glsl")),
  gml: createLanguageAsyncLoader("gml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gml" */ "highlight.js/lib/languages/gml")),
  go: createLanguageAsyncLoader("go", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_go" */ "highlight.js/lib/languages/go")),
  golo: createLanguageAsyncLoader("golo", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_golo" */ "highlight.js/lib/languages/golo")),
  gradle: createLanguageAsyncLoader("gradle", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_gradle" */ "highlight.js/lib/languages/gradle")),
  groovy: createLanguageAsyncLoader("groovy", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_groovy" */ "highlight.js/lib/languages/groovy")),
  haml: createLanguageAsyncLoader("haml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_haml" */ "highlight.js/lib/languages/haml")),
  handlebars: createLanguageAsyncLoader("handlebars", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_handlebars" */ "highlight.js/lib/languages/handlebars")),
  haskell: createLanguageAsyncLoader("haskell", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_haskell" */ "highlight.js/lib/languages/haskell")),
  haxe: createLanguageAsyncLoader("haxe", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_haxe" */ "highlight.js/lib/languages/haxe")),
  hsp: createLanguageAsyncLoader("hsp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_hsp" */ "highlight.js/lib/languages/hsp")),
  htmlbars: createLanguageAsyncLoader("htmlbars", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_htmlbars" */ "highlight.js/lib/languages/htmlbars")),
  http: createLanguageAsyncLoader("http", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_http" */ "highlight.js/lib/languages/http")),
  hy: createLanguageAsyncLoader("hy", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_hy" */ "highlight.js/lib/languages/hy")),
  inform7: createLanguageAsyncLoader("inform7", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_inform7" */ "highlight.js/lib/languages/inform7")),
  ini: createLanguageAsyncLoader("ini", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ini" */ "highlight.js/lib/languages/ini")),
  irpf90: createLanguageAsyncLoader("irpf90", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_irpf90" */ "highlight.js/lib/languages/irpf90")),
  isbl: createLanguageAsyncLoader("isbl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_isbl" */ "highlight.js/lib/languages/isbl")),
  java: createLanguageAsyncLoader("java", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_java" */ "highlight.js/lib/languages/java")),
  javascript: createLanguageAsyncLoader("javascript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_javascript" */ "highlight.js/lib/languages/javascript")),
  jbossCli: createLanguageAsyncLoader("jbossCli", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_jbossCli" */ "highlight.js/lib/languages/jboss-cli")),
  json: createLanguageAsyncLoader("json", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_json" */ "highlight.js/lib/languages/json")),
  juliaRepl: createLanguageAsyncLoader("juliaRepl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_juliaRepl" */ "highlight.js/lib/languages/julia-repl")),
  julia: createLanguageAsyncLoader("julia", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_julia" */ "highlight.js/lib/languages/julia")),
  kotlin: createLanguageAsyncLoader("kotlin", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_kotlin" */ "highlight.js/lib/languages/kotlin")),
  lasso: createLanguageAsyncLoader("lasso", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_lasso" */ "highlight.js/lib/languages/lasso")),
  latex: createLanguageAsyncLoader("latex", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_latex" */ "highlight.js/lib/languages/latex")),
  ldif: createLanguageAsyncLoader("ldif", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ldif" */ "highlight.js/lib/languages/ldif")),
  leaf: createLanguageAsyncLoader("leaf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_leaf" */ "highlight.js/lib/languages/leaf")),
  less: createLanguageAsyncLoader("less", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_less" */ "highlight.js/lib/languages/less")),
  lisp: createLanguageAsyncLoader("lisp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_lisp" */ "highlight.js/lib/languages/lisp")),
  livecodeserver: createLanguageAsyncLoader("livecodeserver", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_livecodeserver" */ "highlight.js/lib/languages/livecodeserver")),
  livescript: createLanguageAsyncLoader("livescript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_livescript" */ "highlight.js/lib/languages/livescript")),
  llvm: createLanguageAsyncLoader("llvm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_llvm" */ "highlight.js/lib/languages/llvm")),
  lsl: createLanguageAsyncLoader("lsl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_lsl" */ "highlight.js/lib/languages/lsl")),
  lua: createLanguageAsyncLoader("lua", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_lua" */ "highlight.js/lib/languages/lua")),
  makefile: createLanguageAsyncLoader("makefile", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_makefile" */ "highlight.js/lib/languages/makefile")),
  markdown: createLanguageAsyncLoader("markdown", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_markdown" */ "highlight.js/lib/languages/markdown")),
  mathematica: createLanguageAsyncLoader("mathematica", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mathematica" */ "highlight.js/lib/languages/mathematica")),
  matlab: createLanguageAsyncLoader("matlab", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_matlab" */ "highlight.js/lib/languages/matlab")),
  maxima: createLanguageAsyncLoader("maxima", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_maxima" */ "highlight.js/lib/languages/maxima")),
  mel: createLanguageAsyncLoader("mel", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mel" */ "highlight.js/lib/languages/mel")),
  mercury: createLanguageAsyncLoader("mercury", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mercury" */ "highlight.js/lib/languages/mercury")),
  mipsasm: createLanguageAsyncLoader("mipsasm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mipsasm" */ "highlight.js/lib/languages/mipsasm")),
  mizar: createLanguageAsyncLoader("mizar", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mizar" */ "highlight.js/lib/languages/mizar")),
  mojolicious: createLanguageAsyncLoader("mojolicious", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_mojolicious" */ "highlight.js/lib/languages/mojolicious")),
  monkey: createLanguageAsyncLoader("monkey", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_monkey" */ "highlight.js/lib/languages/monkey")),
  moonscript: createLanguageAsyncLoader("moonscript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_moonscript" */ "highlight.js/lib/languages/moonscript")),
  n1ql: createLanguageAsyncLoader("n1ql", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_n1ql" */ "highlight.js/lib/languages/n1ql")),
  nginx: createLanguageAsyncLoader("nginx", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_nginx" */ "highlight.js/lib/languages/nginx")),
  nim: createLanguageAsyncLoader("nim", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_nim" */ "highlight.js/lib/languages/nim")),
  nix: createLanguageAsyncLoader("nix", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_nix" */ "highlight.js/lib/languages/nix")),
  nodeRepl: createLanguageAsyncLoader("nodeRepl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_nodeRepl" */ "highlight.js/lib/languages/node-repl")),
  nsis: createLanguageAsyncLoader("nsis", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_nsis" */ "highlight.js/lib/languages/nsis")),
  objectivec: createLanguageAsyncLoader("objectivec", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_objectivec" */ "highlight.js/lib/languages/objectivec")),
  ocaml: createLanguageAsyncLoader("ocaml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ocaml" */ "highlight.js/lib/languages/ocaml")),
  openscad: createLanguageAsyncLoader("openscad", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_openscad" */ "highlight.js/lib/languages/openscad")),
  oxygene: createLanguageAsyncLoader("oxygene", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_oxygene" */ "highlight.js/lib/languages/oxygene")),
  parser3: createLanguageAsyncLoader("parser3", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_parser3" */ "highlight.js/lib/languages/parser3")),
  perl: createLanguageAsyncLoader("perl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_perl" */ "highlight.js/lib/languages/perl")),
  pf: createLanguageAsyncLoader("pf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_pf" */ "highlight.js/lib/languages/pf")),
  pgsql: createLanguageAsyncLoader("pgsql", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_pgsql" */ "highlight.js/lib/languages/pgsql")),
  phpTemplate: createLanguageAsyncLoader("phpTemplate", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_phpTemplate" */ "highlight.js/lib/languages/php-template")),
  php: createLanguageAsyncLoader("php", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_php" */ "highlight.js/lib/languages/php")),
  plaintext: createLanguageAsyncLoader("plaintext", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_plaintext" */ "highlight.js/lib/languages/plaintext")),
  pony: createLanguageAsyncLoader("pony", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_pony" */ "highlight.js/lib/languages/pony")),
  powershell: createLanguageAsyncLoader("powershell", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_powershell" */ "highlight.js/lib/languages/powershell")),
  processing: createLanguageAsyncLoader("processing", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_processing" */ "highlight.js/lib/languages/processing")),
  profile: createLanguageAsyncLoader("profile", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_profile" */ "highlight.js/lib/languages/profile")),
  prolog: createLanguageAsyncLoader("prolog", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_prolog" */ "highlight.js/lib/languages/prolog")),
  properties: createLanguageAsyncLoader("properties", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_properties" */ "highlight.js/lib/languages/properties")),
  protobuf: createLanguageAsyncLoader("protobuf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_protobuf" */ "highlight.js/lib/languages/protobuf")),
  puppet: createLanguageAsyncLoader("puppet", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_puppet" */ "highlight.js/lib/languages/puppet")),
  purebasic: createLanguageAsyncLoader("purebasic", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_purebasic" */ "highlight.js/lib/languages/purebasic")),
  pythonRepl: createLanguageAsyncLoader("pythonRepl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_pythonRepl" */ "highlight.js/lib/languages/python-repl")),
  python: createLanguageAsyncLoader("python", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_python" */ "highlight.js/lib/languages/python")),
  q: createLanguageAsyncLoader("q", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_q" */ "highlight.js/lib/languages/q")),
  qml: createLanguageAsyncLoader("qml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_qml" */ "highlight.js/lib/languages/qml")),
  r: createLanguageAsyncLoader("r", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_r" */ "highlight.js/lib/languages/r")),
  reasonml: createLanguageAsyncLoader("reasonml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_reasonml" */ "highlight.js/lib/languages/reasonml")),
  rib: createLanguageAsyncLoader("rib", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_rib" */ "highlight.js/lib/languages/rib")),
  roboconf: createLanguageAsyncLoader("roboconf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_roboconf" */ "highlight.js/lib/languages/roboconf")),
  routeros: createLanguageAsyncLoader("routeros", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_routeros" */ "highlight.js/lib/languages/routeros")),
  rsl: createLanguageAsyncLoader("rsl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_rsl" */ "highlight.js/lib/languages/rsl")),
  ruby: createLanguageAsyncLoader("ruby", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ruby" */ "highlight.js/lib/languages/ruby")),
  ruleslanguage: createLanguageAsyncLoader("ruleslanguage", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_ruleslanguage" */ "highlight.js/lib/languages/ruleslanguage")),
  rust: createLanguageAsyncLoader("rust", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_rust" */ "highlight.js/lib/languages/rust")),
  sas: createLanguageAsyncLoader("sas", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_sas" */ "highlight.js/lib/languages/sas")),
  scala: createLanguageAsyncLoader("scala", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_scala" */ "highlight.js/lib/languages/scala")),
  scheme: createLanguageAsyncLoader("scheme", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_scheme" */ "highlight.js/lib/languages/scheme")),
  scilab: createLanguageAsyncLoader("scilab", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_scilab" */ "highlight.js/lib/languages/scilab")),
  scss: createLanguageAsyncLoader("scss", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_scss" */ "highlight.js/lib/languages/scss")),
  shell: createLanguageAsyncLoader("shell", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_shell" */ "highlight.js/lib/languages/shell")),
  smali: createLanguageAsyncLoader("smali", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_smali" */ "highlight.js/lib/languages/smali")),
  smalltalk: createLanguageAsyncLoader("smalltalk", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_smalltalk" */ "highlight.js/lib/languages/smalltalk")),
  sml: createLanguageAsyncLoader("sml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_sml" */ "highlight.js/lib/languages/sml")),
  sqf: createLanguageAsyncLoader("sqf", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_sqf" */ "highlight.js/lib/languages/sqf")),
  sql: createLanguageAsyncLoader("sql", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_sql" */ "highlight.js/lib/languages/sql")),
  sqlMore: createLanguageAsyncLoader("sqlMore", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_sqlMore" */ "highlight.js/lib/languages/sql_more")),
  stan: createLanguageAsyncLoader("stan", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_stan" */ "highlight.js/lib/languages/stan")),
  stata: createLanguageAsyncLoader("stata", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_stata" */ "highlight.js/lib/languages/stata")),
  step21: createLanguageAsyncLoader("step21", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_step21" */ "highlight.js/lib/languages/step21")),
  stylus: createLanguageAsyncLoader("stylus", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_stylus" */ "highlight.js/lib/languages/stylus")),
  subunit: createLanguageAsyncLoader("subunit", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_subunit" */ "highlight.js/lib/languages/subunit")),
  swift: createLanguageAsyncLoader("swift", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_swift" */ "highlight.js/lib/languages/swift")),
  taggerscript: createLanguageAsyncLoader("taggerscript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_taggerscript" */ "highlight.js/lib/languages/taggerscript")),
  tap: createLanguageAsyncLoader("tap", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_tap" */ "highlight.js/lib/languages/tap")),
  tcl: createLanguageAsyncLoader("tcl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_tcl" */ "highlight.js/lib/languages/tcl")),
  thrift: createLanguageAsyncLoader("thrift", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_thrift" */ "highlight.js/lib/languages/thrift")),
  tp: createLanguageAsyncLoader("tp", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_tp" */ "highlight.js/lib/languages/tp")),
  twig: createLanguageAsyncLoader("twig", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_twig" */ "highlight.js/lib/languages/twig")),
  typescript: createLanguageAsyncLoader("typescript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_typescript" */ "highlight.js/lib/languages/typescript")),
  vala: createLanguageAsyncLoader("vala", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vala" */ "highlight.js/lib/languages/vala")),
  vbnet: createLanguageAsyncLoader("vbnet", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbnet" */ "highlight.js/lib/languages/vbnet")),
  vbscriptHtml: createLanguageAsyncLoader("vbscriptHtml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbscriptHtml" */ "highlight.js/lib/languages/vbscript-html")),
  vbscript: createLanguageAsyncLoader("vbscript", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vbscript" */ "highlight.js/lib/languages/vbscript")),
  verilog: createLanguageAsyncLoader("verilog", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_verilog" */ "highlight.js/lib/languages/verilog")),
  vhdl: createLanguageAsyncLoader("vhdl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vhdl" */ "highlight.js/lib/languages/vhdl")),
  vim: createLanguageAsyncLoader("vim", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_vim" */ "highlight.js/lib/languages/vim")),
  x86asm: createLanguageAsyncLoader("x86asm", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_x86asm" */ "highlight.js/lib/languages/x86asm")),
  xl: createLanguageAsyncLoader("xl", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_xl" */ "highlight.js/lib/languages/xl")),
  xml: createLanguageAsyncLoader("xml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_xml" */ "highlight.js/lib/languages/xml")),
  xquery: createLanguageAsyncLoader("xquery", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_xquery" */ "highlight.js/lib/languages/xquery")),
  yaml: createLanguageAsyncLoader("yaml", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_yaml" */ "highlight.js/lib/languages/yaml")),
  zephir: createLanguageAsyncLoader("zephir", () => import(/* webpackChunkName: "react-syntax-highlighter_languages_highlight_zephir" */ "highlight.js/lib/languages/zephir")),
}