{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport CredentialsProvider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/admin/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { startOfDay, endOfDay, startOfMonth, endOfMonth, subDays, subMonths, format } from 'date-fns'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const now = new Date()\n    const today = startOfDay(now)\n    const endToday = endOfDay(now)\n    const thisMonth = startOfMonth(now)\n    const endThisMonth = endOfMonth(now)\n    const lastMonth = startOfMonth(subMonths(now, 1))\n    const endLastMonth = endOfMonth(subMonths(now, 1))\n\n    // 获取今日销售数据\n    const todayOrders = await prisma.order.findMany({\n      where: {\n        createdAt: {\n          gte: today,\n          lte: endToday\n        },\n        status: 'COMPLETED'\n      }\n    })\n\n    // 获取本月销售数据\n    const thisMonthOrders = await prisma.order.findMany({\n      where: {\n        createdAt: {\n          gte: thisMonth,\n          lte: endThisMonth\n        },\n        status: 'COMPLETED'\n      }\n    })\n\n    // 获取上月销售数据\n    const lastMonthOrders = await prisma.order.findMany({\n      where: {\n        createdAt: {\n          gte: lastMonth,\n          lte: endLastMonth\n        },\n        status: 'COMPLETED'\n      }\n    })\n\n    // 获取最近7天的销售趋势\n    const salesTrend = []\n    for (let i = 6; i >= 0; i--) {\n      const date = subDays(now, i)\n      const dayStart = startOfDay(date)\n      const dayEnd = endOfDay(date)\n      \n      const dayOrders = await prisma.order.findMany({\n        where: {\n          createdAt: {\n            gte: dayStart,\n            lte: dayEnd\n          },\n          status: 'COMPLETED'\n        }\n      })\n\n      salesTrend.push({\n        date: format(date, 'MM-dd'),\n        sales: dayOrders.reduce((sum, order) => sum + order.totalAmount, 0),\n        orders: dayOrders.length\n      })\n    }\n\n    // 获取热门商品\n    const popularProducts = await prisma.order.groupBy({\n      by: ['productId'],\n      where: {\n        status: 'COMPLETED',\n        createdAt: {\n          gte: thisMonth\n        }\n      },\n      _count: {\n        id: true\n      },\n      _sum: {\n        totalAmount: true\n      },\n      orderBy: {\n        _count: {\n          id: 'desc'\n        }\n      },\n      take: 5\n    })\n\n    // 获取商品详细信息\n    const popularProductsWithDetails = await Promise.all(\n      popularProducts.map(async (item) => {\n        const product = await prisma.product.findUnique({\n          where: { id: item.productId },\n          select: { name: true, image: true }\n        })\n        return {\n          ...item,\n          product\n        }\n      })\n    )\n\n    // 获取收入统计\n    const revenueStats = await prisma.order.aggregate({\n      where: {\n        status: 'COMPLETED'\n      },\n      _sum: {\n        totalAmount: true\n      },\n      _count: {\n        id: true\n      }\n    })\n\n    // 计算统计数据\n    const todayRevenue = todayOrders.reduce((sum, order) => sum + order.totalAmount, 0)\n    const thisMonthRevenue = thisMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)\n    const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)\n    \n    const monthlyGrowth = lastMonthRevenue > 0 \n      ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 \n      : 0\n\n    // 获取用户增长数据\n    const totalUsers = await prisma.user.count()\n    const thisMonthUsers = await prisma.user.count({\n      where: {\n        createdAt: {\n          gte: thisMonth\n        }\n      }\n    })\n\n    // 获取商品库存警告\n    const lowStockProducts = await prisma.product.findMany({\n      where: {\n        status: 'ACTIVE'\n      },\n      include: {\n        _count: {\n          select: {\n            cards: {\n              where: {\n                status: 'AVAILABLE'\n              }\n            }\n          }\n        }\n      }\n    })\n\n    const lowStockWarnings = lowStockProducts\n      .filter(product => product._count.cards < 10)\n      .map(product => ({\n        id: product.id,\n        name: product.name,\n        stock: product._count.cards\n      }))\n\n    return NextResponse.json({\n      todayRevenue,\n      thisMonthRevenue,\n      lastMonthRevenue,\n      monthlyGrowth,\n      todayOrders: todayOrders.length,\n      thisMonthOrders: thisMonthOrders.length,\n      totalRevenue: revenueStats._sum.totalAmount || 0,\n      totalOrders: revenueStats._count || 0,\n      totalUsers,\n      thisMonthUsers,\n      salesTrend,\n      popularProducts: popularProductsWithDetails,\n      lowStockWarnings\n    })\n\n  } catch (error) {\n    console.error('获取统计数据失败:', error)\n    return NextResponse.json({ error: '获取统计数据失败' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QACzB,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;QAC/B,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QAChC,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAC9C,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAE/C,WAAW;QACX,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC9C,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;gBACA,QAAQ;YACV;QACF;QAEA,WAAW;QACX,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAClD,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;gBACA,QAAQ;YACV;QACF;QAEA,WAAW;QACX,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAClD,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;gBACA,QAAQ;YACV;QACF;QAEA,cAAc;QACd,MAAM,aAAa,EAAE;QACrB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAC1B,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;YAC5B,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE;YAExB,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC5C,OAAO;oBACL,WAAW;wBACT,KAAK;wBACL,KAAK;oBACP;oBACA,QAAQ;gBACV;YACF;YAEA,WAAW,IAAI,CAAC;gBACd,MAAM,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBACnB,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;gBACjE,QAAQ,UAAU,MAAM;YAC1B;QACF;QAEA,SAAS;QACT,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YACjD,IAAI;gBAAC;aAAY;YACjB,OAAO;gBACL,QAAQ;gBACR,WAAW;oBACT,KAAK;gBACP;YACF;YACA,QAAQ;gBACN,IAAI;YACN;YACA,MAAM;gBACJ,aAAa;YACf;YACA,SAAS;gBACP,QAAQ;oBACN,IAAI;gBACN;YACF;YACA,MAAM;QACR;QAEA,WAAW;QACX,MAAM,6BAA6B,MAAM,QAAQ,GAAG,CAClD,gBAAgB,GAAG,CAAC,OAAO;YACzB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;gBAC5B,QAAQ;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACpC;YACA,OAAO;gBACL,GAAG,IAAI;gBACP;YACF;QACF;QAGF,SAAS;QACT,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YAChD,OAAO;gBACL,QAAQ;YACV;YACA,MAAM;gBACJ,aAAa;YACf;YACA,QAAQ;gBACN,IAAI;YACN;QACF;QAEA,SAAS;QACT,MAAM,eAAe,YAAY,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;QACjF,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;QACzF,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;QAEzF,MAAM,gBAAgB,mBAAmB,IACrC,AAAC,CAAC,mBAAmB,gBAAgB,IAAI,mBAAoB,MAC7D;QAEJ,WAAW;QACX,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;QAC1C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7C,OAAO;gBACL,WAAW;oBACT,KAAK;gBACP;YACF;QACF;QAEA,WAAW;QACX,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACrD,OAAO;gBACL,QAAQ;YACV;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,OAAO;4BACL,OAAO;gCACL,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;QACF;QAEA,MAAM,mBAAmB,iBACtB,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,CAAC,KAAK,GAAG,IACzC,GAAG,CAAC,CAAA,UAAW,CAAC;gBACf,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,MAAM,CAAC,KAAK;YAC7B,CAAC;QAEH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA;YACA;YACA,aAAa,YAAY,MAAM;YAC/B,iBAAiB,gBAAgB,MAAM;YACvC,cAAc,aAAa,IAAI,CAAC,WAAW,IAAI;YAC/C,aAAa,aAAa,MAAM,IAAI;YACpC;YACA;YACA;YACA,iBAAiB;YACjB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAW,GAAG;YAAE,QAAQ;QAAI;IAChE;AACF", "debugId": null}}]}