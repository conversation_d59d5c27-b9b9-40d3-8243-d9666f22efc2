"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));
var _createLanguageAsyncLoader = _interopRequireDefault(require("./create-language-async-loader"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != (0, _typeof2["default"])(e) && "function" != typeof e) return { "default": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n["default"] = e, t && t.set(e, n), n; }
var _default = exports["default"] = {
  abap: (0, _createLanguageAsyncLoader["default"])("abap", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_abap" */"refractor/lang/abap.js"));
    });
  }),
  abnf: (0, _createLanguageAsyncLoader["default"])("abnf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_abnf" */"refractor/lang/abnf.js"));
    });
  }),
  actionscript: (0, _createLanguageAsyncLoader["default"])("actionscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_actionscript" */"refractor/lang/actionscript.js"));
    });
  }),
  ada: (0, _createLanguageAsyncLoader["default"])("ada", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ada" */"refractor/lang/ada.js"));
    });
  }),
  agda: (0, _createLanguageAsyncLoader["default"])("agda", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_agda" */"refractor/lang/agda.js"));
    });
  }),
  al: (0, _createLanguageAsyncLoader["default"])("al", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_al" */"refractor/lang/al.js"));
    });
  }),
  antlr4: (0, _createLanguageAsyncLoader["default"])("antlr4", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_antlr4" */"refractor/lang/antlr4.js"));
    });
  }),
  apacheconf: (0, _createLanguageAsyncLoader["default"])("apacheconf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_apacheconf" */"refractor/lang/apacheconf.js"));
    });
  }),
  apex: (0, _createLanguageAsyncLoader["default"])("apex", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_apex" */"refractor/lang/apex.js"));
    });
  }),
  apl: (0, _createLanguageAsyncLoader["default"])("apl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_apl" */"refractor/lang/apl.js"));
    });
  }),
  applescript: (0, _createLanguageAsyncLoader["default"])("applescript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_applescript" */"refractor/lang/applescript.js"));
    });
  }),
  aql: (0, _createLanguageAsyncLoader["default"])("aql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_aql" */"refractor/lang/aql.js"));
    });
  }),
  arduino: (0, _createLanguageAsyncLoader["default"])("arduino", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_arduino" */"refractor/lang/arduino.js"));
    });
  }),
  arff: (0, _createLanguageAsyncLoader["default"])("arff", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_arff" */"refractor/lang/arff.js"));
    });
  }),
  asciidoc: (0, _createLanguageAsyncLoader["default"])("asciidoc", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_asciidoc" */"refractor/lang/asciidoc.js"));
    });
  }),
  asm6502: (0, _createLanguageAsyncLoader["default"])("asm6502", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_asm6502" */"refractor/lang/asm6502.js"));
    });
  }),
  asmatmel: (0, _createLanguageAsyncLoader["default"])("asmatmel", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_asmatmel" */"refractor/lang/asmatmel.js"));
    });
  }),
  aspnet: (0, _createLanguageAsyncLoader["default"])("aspnet", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_aspnet" */"refractor/lang/aspnet.js"));
    });
  }),
  autohotkey: (0, _createLanguageAsyncLoader["default"])("autohotkey", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_autohotkey" */"refractor/lang/autohotkey.js"));
    });
  }),
  autoit: (0, _createLanguageAsyncLoader["default"])("autoit", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_autoit" */"refractor/lang/autoit.js"));
    });
  }),
  avisynth: (0, _createLanguageAsyncLoader["default"])("avisynth", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_avisynth" */"refractor/lang/avisynth.js"));
    });
  }),
  avroIdl: (0, _createLanguageAsyncLoader["default"])("avroIdl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_avroIdl" */"refractor/lang/avro-idl.js"));
    });
  }),
  bash: (0, _createLanguageAsyncLoader["default"])("bash", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bash" */"refractor/lang/bash.js"));
    });
  }),
  basic: (0, _createLanguageAsyncLoader["default"])("basic", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_basic" */"refractor/lang/basic.js"));
    });
  }),
  batch: (0, _createLanguageAsyncLoader["default"])("batch", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_batch" */"refractor/lang/batch.js"));
    });
  }),
  bbcode: (0, _createLanguageAsyncLoader["default"])("bbcode", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bbcode" */"refractor/lang/bbcode.js"));
    });
  }),
  bicep: (0, _createLanguageAsyncLoader["default"])("bicep", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bicep" */"refractor/lang/bicep.js"));
    });
  }),
  birb: (0, _createLanguageAsyncLoader["default"])("birb", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_birb" */"refractor/lang/birb.js"));
    });
  }),
  bison: (0, _createLanguageAsyncLoader["default"])("bison", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bison" */"refractor/lang/bison.js"));
    });
  }),
  bnf: (0, _createLanguageAsyncLoader["default"])("bnf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bnf" */"refractor/lang/bnf.js"));
    });
  }),
  brainfuck: (0, _createLanguageAsyncLoader["default"])("brainfuck", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_brainfuck" */"refractor/lang/brainfuck.js"));
    });
  }),
  brightscript: (0, _createLanguageAsyncLoader["default"])("brightscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_brightscript" */"refractor/lang/brightscript.js"));
    });
  }),
  bro: (0, _createLanguageAsyncLoader["default"])("bro", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bro" */"refractor/lang/bro.js"));
    });
  }),
  bsl: (0, _createLanguageAsyncLoader["default"])("bsl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_bsl" */"refractor/lang/bsl.js"));
    });
  }),
  c: (0, _createLanguageAsyncLoader["default"])("c", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_c" */"refractor/lang/c.js"));
    });
  }),
  cfscript: (0, _createLanguageAsyncLoader["default"])("cfscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cfscript" */"refractor/lang/cfscript.js"));
    });
  }),
  chaiscript: (0, _createLanguageAsyncLoader["default"])("chaiscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_chaiscript" */"refractor/lang/chaiscript.js"));
    });
  }),
  cil: (0, _createLanguageAsyncLoader["default"])("cil", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cil" */"refractor/lang/cil.js"));
    });
  }),
  clike: (0, _createLanguageAsyncLoader["default"])("clike", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_clike" */"refractor/lang/clike.js"));
    });
  }),
  clojure: (0, _createLanguageAsyncLoader["default"])("clojure", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_clojure" */"refractor/lang/clojure.js"));
    });
  }),
  cmake: (0, _createLanguageAsyncLoader["default"])("cmake", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cmake" */"refractor/lang/cmake.js"));
    });
  }),
  cobol: (0, _createLanguageAsyncLoader["default"])("cobol", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cobol" */"refractor/lang/cobol.js"));
    });
  }),
  coffeescript: (0, _createLanguageAsyncLoader["default"])("coffeescript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_coffeescript" */"refractor/lang/coffeescript.js"));
    });
  }),
  concurnas: (0, _createLanguageAsyncLoader["default"])("concurnas", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_concurnas" */"refractor/lang/concurnas.js"));
    });
  }),
  coq: (0, _createLanguageAsyncLoader["default"])("coq", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_coq" */"refractor/lang/coq.js"));
    });
  }),
  cpp: (0, _createLanguageAsyncLoader["default"])("cpp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cpp" */"refractor/lang/cpp.js"));
    });
  }),
  crystal: (0, _createLanguageAsyncLoader["default"])("crystal", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_crystal" */"refractor/lang/crystal.js"));
    });
  }),
  csharp: (0, _createLanguageAsyncLoader["default"])("csharp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_csharp" */"refractor/lang/csharp.js"));
    });
  }),
  cshtml: (0, _createLanguageAsyncLoader["default"])("cshtml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cshtml" */"refractor/lang/cshtml.js"));
    });
  }),
  csp: (0, _createLanguageAsyncLoader["default"])("csp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_csp" */"refractor/lang/csp.js"));
    });
  }),
  cssExtras: (0, _createLanguageAsyncLoader["default"])("cssExtras", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cssExtras" */"refractor/lang/css-extras.js"));
    });
  }),
  css: (0, _createLanguageAsyncLoader["default"])("css", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_css" */"refractor/lang/css.js"));
    });
  }),
  csv: (0, _createLanguageAsyncLoader["default"])("csv", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_csv" */"refractor/lang/csv.js"));
    });
  }),
  cypher: (0, _createLanguageAsyncLoader["default"])("cypher", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_cypher" */"refractor/lang/cypher.js"));
    });
  }),
  d: (0, _createLanguageAsyncLoader["default"])("d", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_d" */"refractor/lang/d.js"));
    });
  }),
  dart: (0, _createLanguageAsyncLoader["default"])("dart", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dart" */"refractor/lang/dart.js"));
    });
  }),
  dataweave: (0, _createLanguageAsyncLoader["default"])("dataweave", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dataweave" */"refractor/lang/dataweave.js"));
    });
  }),
  dax: (0, _createLanguageAsyncLoader["default"])("dax", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dax" */"refractor/lang/dax.js"));
    });
  }),
  dhall: (0, _createLanguageAsyncLoader["default"])("dhall", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dhall" */"refractor/lang/dhall.js"));
    });
  }),
  diff: (0, _createLanguageAsyncLoader["default"])("diff", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_diff" */"refractor/lang/diff.js"));
    });
  }),
  django: (0, _createLanguageAsyncLoader["default"])("django", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_django" */"refractor/lang/django.js"));
    });
  }),
  dnsZoneFile: (0, _createLanguageAsyncLoader["default"])("dnsZoneFile", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dnsZoneFile" */"refractor/lang/dns-zone-file.js"));
    });
  }),
  docker: (0, _createLanguageAsyncLoader["default"])("docker", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_docker" */"refractor/lang/docker.js"));
    });
  }),
  dot: (0, _createLanguageAsyncLoader["default"])("dot", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_dot" */"refractor/lang/dot.js"));
    });
  }),
  ebnf: (0, _createLanguageAsyncLoader["default"])("ebnf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ebnf" */"refractor/lang/ebnf.js"));
    });
  }),
  editorconfig: (0, _createLanguageAsyncLoader["default"])("editorconfig", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_editorconfig" */"refractor/lang/editorconfig.js"));
    });
  }),
  eiffel: (0, _createLanguageAsyncLoader["default"])("eiffel", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_eiffel" */"refractor/lang/eiffel.js"));
    });
  }),
  ejs: (0, _createLanguageAsyncLoader["default"])("ejs", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ejs" */"refractor/lang/ejs.js"));
    });
  }),
  elixir: (0, _createLanguageAsyncLoader["default"])("elixir", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_elixir" */"refractor/lang/elixir.js"));
    });
  }),
  elm: (0, _createLanguageAsyncLoader["default"])("elm", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_elm" */"refractor/lang/elm.js"));
    });
  }),
  erb: (0, _createLanguageAsyncLoader["default"])("erb", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_erb" */"refractor/lang/erb.js"));
    });
  }),
  erlang: (0, _createLanguageAsyncLoader["default"])("erlang", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_erlang" */"refractor/lang/erlang.js"));
    });
  }),
  etlua: (0, _createLanguageAsyncLoader["default"])("etlua", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_etlua" */"refractor/lang/etlua.js"));
    });
  }),
  excelFormula: (0, _createLanguageAsyncLoader["default"])("excelFormula", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_excelFormula" */"refractor/lang/excel-formula.js"));
    });
  }),
  factor: (0, _createLanguageAsyncLoader["default"])("factor", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_factor" */"refractor/lang/factor.js"));
    });
  }),
  falselang: (0, _createLanguageAsyncLoader["default"])("falselang", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_falselang" */"refractor/lang/false.js"));
    });
  }),
  firestoreSecurityRules: (0, _createLanguageAsyncLoader["default"])("firestoreSecurityRules", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_firestoreSecurityRules" */"refractor/lang/firestore-security-rules.js"));
    });
  }),
  flow: (0, _createLanguageAsyncLoader["default"])("flow", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_flow" */"refractor/lang/flow.js"));
    });
  }),
  fortran: (0, _createLanguageAsyncLoader["default"])("fortran", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_fortran" */"refractor/lang/fortran.js"));
    });
  }),
  fsharp: (0, _createLanguageAsyncLoader["default"])("fsharp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_fsharp" */"refractor/lang/fsharp.js"));
    });
  }),
  ftl: (0, _createLanguageAsyncLoader["default"])("ftl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ftl" */"refractor/lang/ftl.js"));
    });
  }),
  gap: (0, _createLanguageAsyncLoader["default"])("gap", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gap" */"refractor/lang/gap.js"));
    });
  }),
  gcode: (0, _createLanguageAsyncLoader["default"])("gcode", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gcode" */"refractor/lang/gcode.js"));
    });
  }),
  gdscript: (0, _createLanguageAsyncLoader["default"])("gdscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gdscript" */"refractor/lang/gdscript.js"));
    });
  }),
  gedcom: (0, _createLanguageAsyncLoader["default"])("gedcom", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gedcom" */"refractor/lang/gedcom.js"));
    });
  }),
  gherkin: (0, _createLanguageAsyncLoader["default"])("gherkin", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gherkin" */"refractor/lang/gherkin.js"));
    });
  }),
  git: (0, _createLanguageAsyncLoader["default"])("git", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_git" */"refractor/lang/git.js"));
    });
  }),
  glsl: (0, _createLanguageAsyncLoader["default"])("glsl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_glsl" */"refractor/lang/glsl.js"));
    });
  }),
  gml: (0, _createLanguageAsyncLoader["default"])("gml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gml" */"refractor/lang/gml.js"));
    });
  }),
  gn: (0, _createLanguageAsyncLoader["default"])("gn", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_gn" */"refractor/lang/gn.js"));
    });
  }),
  goModule: (0, _createLanguageAsyncLoader["default"])("goModule", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_goModule" */"refractor/lang/go-module.js"));
    });
  }),
  go: (0, _createLanguageAsyncLoader["default"])("go", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_go" */"refractor/lang/go.js"));
    });
  }),
  graphql: (0, _createLanguageAsyncLoader["default"])("graphql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_graphql" */"refractor/lang/graphql.js"));
    });
  }),
  groovy: (0, _createLanguageAsyncLoader["default"])("groovy", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_groovy" */"refractor/lang/groovy.js"));
    });
  }),
  haml: (0, _createLanguageAsyncLoader["default"])("haml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_haml" */"refractor/lang/haml.js"));
    });
  }),
  handlebars: (0, _createLanguageAsyncLoader["default"])("handlebars", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_handlebars" */"refractor/lang/handlebars.js"));
    });
  }),
  haskell: (0, _createLanguageAsyncLoader["default"])("haskell", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_haskell" */"refractor/lang/haskell.js"));
    });
  }),
  haxe: (0, _createLanguageAsyncLoader["default"])("haxe", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_haxe" */"refractor/lang/haxe.js"));
    });
  }),
  hcl: (0, _createLanguageAsyncLoader["default"])("hcl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_hcl" */"refractor/lang/hcl.js"));
    });
  }),
  hlsl: (0, _createLanguageAsyncLoader["default"])("hlsl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_hlsl" */"refractor/lang/hlsl.js"));
    });
  }),
  hoon: (0, _createLanguageAsyncLoader["default"])("hoon", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_hoon" */"refractor/lang/hoon.js"));
    });
  }),
  hpkp: (0, _createLanguageAsyncLoader["default"])("hpkp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_hpkp" */"refractor/lang/hpkp.js"));
    });
  }),
  hsts: (0, _createLanguageAsyncLoader["default"])("hsts", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_hsts" */"refractor/lang/hsts.js"));
    });
  }),
  http: (0, _createLanguageAsyncLoader["default"])("http", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_http" */"refractor/lang/http.js"));
    });
  }),
  ichigojam: (0, _createLanguageAsyncLoader["default"])("ichigojam", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ichigojam" */"refractor/lang/ichigojam.js"));
    });
  }),
  icon: (0, _createLanguageAsyncLoader["default"])("icon", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_icon" */"refractor/lang/icon.js"));
    });
  }),
  icuMessageFormat: (0, _createLanguageAsyncLoader["default"])("icuMessageFormat", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_icuMessageFormat" */"refractor/lang/icu-message-format.js"));
    });
  }),
  idris: (0, _createLanguageAsyncLoader["default"])("idris", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_idris" */"refractor/lang/idris.js"));
    });
  }),
  iecst: (0, _createLanguageAsyncLoader["default"])("iecst", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_iecst" */"refractor/lang/iecst.js"));
    });
  }),
  ignore: (0, _createLanguageAsyncLoader["default"])("ignore", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ignore" */"refractor/lang/ignore.js"));
    });
  }),
  inform7: (0, _createLanguageAsyncLoader["default"])("inform7", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_inform7" */"refractor/lang/inform7.js"));
    });
  }),
  ini: (0, _createLanguageAsyncLoader["default"])("ini", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ini" */"refractor/lang/ini.js"));
    });
  }),
  io: (0, _createLanguageAsyncLoader["default"])("io", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_io" */"refractor/lang/io.js"));
    });
  }),
  j: (0, _createLanguageAsyncLoader["default"])("j", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_j" */"refractor/lang/j.js"));
    });
  }),
  java: (0, _createLanguageAsyncLoader["default"])("java", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_java" */"refractor/lang/java.js"));
    });
  }),
  javadoc: (0, _createLanguageAsyncLoader["default"])("javadoc", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_javadoc" */"refractor/lang/javadoc.js"));
    });
  }),
  javadoclike: (0, _createLanguageAsyncLoader["default"])("javadoclike", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_javadoclike" */"refractor/lang/javadoclike.js"));
    });
  }),
  javascript: (0, _createLanguageAsyncLoader["default"])("javascript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_javascript" */"refractor/lang/javascript.js"));
    });
  }),
  javastacktrace: (0, _createLanguageAsyncLoader["default"])("javastacktrace", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_javastacktrace" */"refractor/lang/javastacktrace.js"));
    });
  }),
  jexl: (0, _createLanguageAsyncLoader["default"])("jexl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jexl" */"refractor/lang/jexl.js"));
    });
  }),
  jolie: (0, _createLanguageAsyncLoader["default"])("jolie", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jolie" */"refractor/lang/jolie.js"));
    });
  }),
  jq: (0, _createLanguageAsyncLoader["default"])("jq", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jq" */"refractor/lang/jq.js"));
    });
  }),
  jsExtras: (0, _createLanguageAsyncLoader["default"])("jsExtras", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsExtras" */"refractor/lang/js-extras.js"));
    });
  }),
  jsTemplates: (0, _createLanguageAsyncLoader["default"])("jsTemplates", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsTemplates" */"refractor/lang/js-templates.js"));
    });
  }),
  jsdoc: (0, _createLanguageAsyncLoader["default"])("jsdoc", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsdoc" */"refractor/lang/jsdoc.js"));
    });
  }),
  json: (0, _createLanguageAsyncLoader["default"])("json", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_json" */"refractor/lang/json.js"));
    });
  }),
  json5: (0, _createLanguageAsyncLoader["default"])("json5", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_json5" */"refractor/lang/json5.js"));
    });
  }),
  jsonp: (0, _createLanguageAsyncLoader["default"])("jsonp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsonp" */"refractor/lang/jsonp.js"));
    });
  }),
  jsstacktrace: (0, _createLanguageAsyncLoader["default"])("jsstacktrace", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsstacktrace" */"refractor/lang/jsstacktrace.js"));
    });
  }),
  jsx: (0, _createLanguageAsyncLoader["default"])("jsx", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_jsx" */"refractor/lang/jsx.js"));
    });
  }),
  julia: (0, _createLanguageAsyncLoader["default"])("julia", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_julia" */"refractor/lang/julia.js"));
    });
  }),
  keepalived: (0, _createLanguageAsyncLoader["default"])("keepalived", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_keepalived" */"refractor/lang/keepalived.js"));
    });
  }),
  keyman: (0, _createLanguageAsyncLoader["default"])("keyman", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_keyman" */"refractor/lang/keyman.js"));
    });
  }),
  kotlin: (0, _createLanguageAsyncLoader["default"])("kotlin", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_kotlin" */"refractor/lang/kotlin.js"));
    });
  }),
  kumir: (0, _createLanguageAsyncLoader["default"])("kumir", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_kumir" */"refractor/lang/kumir.js"));
    });
  }),
  kusto: (0, _createLanguageAsyncLoader["default"])("kusto", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_kusto" */"refractor/lang/kusto.js"));
    });
  }),
  latex: (0, _createLanguageAsyncLoader["default"])("latex", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_latex" */"refractor/lang/latex.js"));
    });
  }),
  latte: (0, _createLanguageAsyncLoader["default"])("latte", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_latte" */"refractor/lang/latte.js"));
    });
  }),
  less: (0, _createLanguageAsyncLoader["default"])("less", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_less" */"refractor/lang/less.js"));
    });
  }),
  lilypond: (0, _createLanguageAsyncLoader["default"])("lilypond", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_lilypond" */"refractor/lang/lilypond.js"));
    });
  }),
  liquid: (0, _createLanguageAsyncLoader["default"])("liquid", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_liquid" */"refractor/lang/liquid.js"));
    });
  }),
  lisp: (0, _createLanguageAsyncLoader["default"])("lisp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_lisp" */"refractor/lang/lisp.js"));
    });
  }),
  livescript: (0, _createLanguageAsyncLoader["default"])("livescript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_livescript" */"refractor/lang/livescript.js"));
    });
  }),
  llvm: (0, _createLanguageAsyncLoader["default"])("llvm", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_llvm" */"refractor/lang/llvm.js"));
    });
  }),
  log: (0, _createLanguageAsyncLoader["default"])("log", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_log" */"refractor/lang/log.js"));
    });
  }),
  lolcode: (0, _createLanguageAsyncLoader["default"])("lolcode", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_lolcode" */"refractor/lang/lolcode.js"));
    });
  }),
  lua: (0, _createLanguageAsyncLoader["default"])("lua", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_lua" */"refractor/lang/lua.js"));
    });
  }),
  magma: (0, _createLanguageAsyncLoader["default"])("magma", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_magma" */"refractor/lang/magma.js"));
    });
  }),
  makefile: (0, _createLanguageAsyncLoader["default"])("makefile", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_makefile" */"refractor/lang/makefile.js"));
    });
  }),
  markdown: (0, _createLanguageAsyncLoader["default"])("markdown", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_markdown" */"refractor/lang/markdown.js"));
    });
  }),
  markupTemplating: (0, _createLanguageAsyncLoader["default"])("markupTemplating", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_markupTemplating" */"refractor/lang/markup-templating.js"));
    });
  }),
  markup: (0, _createLanguageAsyncLoader["default"])("markup", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_markup" */"refractor/lang/markup.js"));
    });
  }),
  matlab: (0, _createLanguageAsyncLoader["default"])("matlab", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_matlab" */"refractor/lang/matlab.js"));
    });
  }),
  maxscript: (0, _createLanguageAsyncLoader["default"])("maxscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_maxscript" */"refractor/lang/maxscript.js"));
    });
  }),
  mel: (0, _createLanguageAsyncLoader["default"])("mel", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_mel" */"refractor/lang/mel.js"));
    });
  }),
  mermaid: (0, _createLanguageAsyncLoader["default"])("mermaid", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_mermaid" */"refractor/lang/mermaid.js"));
    });
  }),
  mizar: (0, _createLanguageAsyncLoader["default"])("mizar", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_mizar" */"refractor/lang/mizar.js"));
    });
  }),
  mongodb: (0, _createLanguageAsyncLoader["default"])("mongodb", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_mongodb" */"refractor/lang/mongodb.js"));
    });
  }),
  monkey: (0, _createLanguageAsyncLoader["default"])("monkey", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_monkey" */"refractor/lang/monkey.js"));
    });
  }),
  moonscript: (0, _createLanguageAsyncLoader["default"])("moonscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_moonscript" */"refractor/lang/moonscript.js"));
    });
  }),
  n1ql: (0, _createLanguageAsyncLoader["default"])("n1ql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_n1ql" */"refractor/lang/n1ql.js"));
    });
  }),
  n4js: (0, _createLanguageAsyncLoader["default"])("n4js", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_n4js" */"refractor/lang/n4js.js"));
    });
  }),
  nand2tetrisHdl: (0, _createLanguageAsyncLoader["default"])("nand2tetrisHdl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nand2tetrisHdl" */"refractor/lang/nand2tetris-hdl.js"));
    });
  }),
  naniscript: (0, _createLanguageAsyncLoader["default"])("naniscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_naniscript" */"refractor/lang/naniscript.js"));
    });
  }),
  nasm: (0, _createLanguageAsyncLoader["default"])("nasm", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nasm" */"refractor/lang/nasm.js"));
    });
  }),
  neon: (0, _createLanguageAsyncLoader["default"])("neon", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_neon" */"refractor/lang/neon.js"));
    });
  }),
  nevod: (0, _createLanguageAsyncLoader["default"])("nevod", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nevod" */"refractor/lang/nevod.js"));
    });
  }),
  nginx: (0, _createLanguageAsyncLoader["default"])("nginx", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nginx" */"refractor/lang/nginx.js"));
    });
  }),
  nim: (0, _createLanguageAsyncLoader["default"])("nim", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nim" */"refractor/lang/nim.js"));
    });
  }),
  nix: (0, _createLanguageAsyncLoader["default"])("nix", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nix" */"refractor/lang/nix.js"));
    });
  }),
  nsis: (0, _createLanguageAsyncLoader["default"])("nsis", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_nsis" */"refractor/lang/nsis.js"));
    });
  }),
  objectivec: (0, _createLanguageAsyncLoader["default"])("objectivec", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_objectivec" */"refractor/lang/objectivec.js"));
    });
  }),
  ocaml: (0, _createLanguageAsyncLoader["default"])("ocaml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ocaml" */"refractor/lang/ocaml.js"));
    });
  }),
  opencl: (0, _createLanguageAsyncLoader["default"])("opencl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_opencl" */"refractor/lang/opencl.js"));
    });
  }),
  openqasm: (0, _createLanguageAsyncLoader["default"])("openqasm", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_openqasm" */"refractor/lang/openqasm.js"));
    });
  }),
  oz: (0, _createLanguageAsyncLoader["default"])("oz", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_oz" */"refractor/lang/oz.js"));
    });
  }),
  parigp: (0, _createLanguageAsyncLoader["default"])("parigp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_parigp" */"refractor/lang/parigp.js"));
    });
  }),
  parser: (0, _createLanguageAsyncLoader["default"])("parser", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_parser" */"refractor/lang/parser.js"));
    });
  }),
  pascal: (0, _createLanguageAsyncLoader["default"])("pascal", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_pascal" */"refractor/lang/pascal.js"));
    });
  }),
  pascaligo: (0, _createLanguageAsyncLoader["default"])("pascaligo", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_pascaligo" */"refractor/lang/pascaligo.js"));
    });
  }),
  pcaxis: (0, _createLanguageAsyncLoader["default"])("pcaxis", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_pcaxis" */"refractor/lang/pcaxis.js"));
    });
  }),
  peoplecode: (0, _createLanguageAsyncLoader["default"])("peoplecode", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_peoplecode" */"refractor/lang/peoplecode.js"));
    });
  }),
  perl: (0, _createLanguageAsyncLoader["default"])("perl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_perl" */"refractor/lang/perl.js"));
    });
  }),
  phpExtras: (0, _createLanguageAsyncLoader["default"])("phpExtras", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_phpExtras" */"refractor/lang/php-extras.js"));
    });
  }),
  php: (0, _createLanguageAsyncLoader["default"])("php", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_php" */"refractor/lang/php.js"));
    });
  }),
  phpdoc: (0, _createLanguageAsyncLoader["default"])("phpdoc", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_phpdoc" */"refractor/lang/phpdoc.js"));
    });
  }),
  plsql: (0, _createLanguageAsyncLoader["default"])("plsql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_plsql" */"refractor/lang/plsql.js"));
    });
  }),
  powerquery: (0, _createLanguageAsyncLoader["default"])("powerquery", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_powerquery" */"refractor/lang/powerquery.js"));
    });
  }),
  powershell: (0, _createLanguageAsyncLoader["default"])("powershell", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_powershell" */"refractor/lang/powershell.js"));
    });
  }),
  processing: (0, _createLanguageAsyncLoader["default"])("processing", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_processing" */"refractor/lang/processing.js"));
    });
  }),
  prolog: (0, _createLanguageAsyncLoader["default"])("prolog", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_prolog" */"refractor/lang/prolog.js"));
    });
  }),
  promql: (0, _createLanguageAsyncLoader["default"])("promql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_promql" */"refractor/lang/promql.js"));
    });
  }),
  properties: (0, _createLanguageAsyncLoader["default"])("properties", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_properties" */"refractor/lang/properties.js"));
    });
  }),
  protobuf: (0, _createLanguageAsyncLoader["default"])("protobuf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_protobuf" */"refractor/lang/protobuf.js"));
    });
  }),
  psl: (0, _createLanguageAsyncLoader["default"])("psl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_psl" */"refractor/lang/psl.js"));
    });
  }),
  pug: (0, _createLanguageAsyncLoader["default"])("pug", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_pug" */"refractor/lang/pug.js"));
    });
  }),
  puppet: (0, _createLanguageAsyncLoader["default"])("puppet", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_puppet" */"refractor/lang/puppet.js"));
    });
  }),
  pure: (0, _createLanguageAsyncLoader["default"])("pure", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_pure" */"refractor/lang/pure.js"));
    });
  }),
  purebasic: (0, _createLanguageAsyncLoader["default"])("purebasic", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_purebasic" */"refractor/lang/purebasic.js"));
    });
  }),
  purescript: (0, _createLanguageAsyncLoader["default"])("purescript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_purescript" */"refractor/lang/purescript.js"));
    });
  }),
  python: (0, _createLanguageAsyncLoader["default"])("python", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_python" */"refractor/lang/python.js"));
    });
  }),
  q: (0, _createLanguageAsyncLoader["default"])("q", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_q" */"refractor/lang/q.js"));
    });
  }),
  qml: (0, _createLanguageAsyncLoader["default"])("qml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_qml" */"refractor/lang/qml.js"));
    });
  }),
  qore: (0, _createLanguageAsyncLoader["default"])("qore", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_qore" */"refractor/lang/qore.js"));
    });
  }),
  qsharp: (0, _createLanguageAsyncLoader["default"])("qsharp", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_qsharp" */"refractor/lang/qsharp.js"));
    });
  }),
  r: (0, _createLanguageAsyncLoader["default"])("r", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_r" */"refractor/lang/r.js"));
    });
  }),
  racket: (0, _createLanguageAsyncLoader["default"])("racket", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_racket" */"refractor/lang/racket.js"));
    });
  }),
  reason: (0, _createLanguageAsyncLoader["default"])("reason", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_reason" */"refractor/lang/reason.js"));
    });
  }),
  regex: (0, _createLanguageAsyncLoader["default"])("regex", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_regex" */"refractor/lang/regex.js"));
    });
  }),
  rego: (0, _createLanguageAsyncLoader["default"])("rego", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_rego" */"refractor/lang/rego.js"));
    });
  }),
  renpy: (0, _createLanguageAsyncLoader["default"])("renpy", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_renpy" */"refractor/lang/renpy.js"));
    });
  }),
  rest: (0, _createLanguageAsyncLoader["default"])("rest", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_rest" */"refractor/lang/rest.js"));
    });
  }),
  rip: (0, _createLanguageAsyncLoader["default"])("rip", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_rip" */"refractor/lang/rip.js"));
    });
  }),
  roboconf: (0, _createLanguageAsyncLoader["default"])("roboconf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_roboconf" */"refractor/lang/roboconf.js"));
    });
  }),
  robotframework: (0, _createLanguageAsyncLoader["default"])("robotframework", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_robotframework" */"refractor/lang/robotframework.js"));
    });
  }),
  ruby: (0, _createLanguageAsyncLoader["default"])("ruby", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_ruby" */"refractor/lang/ruby.js"));
    });
  }),
  rust: (0, _createLanguageAsyncLoader["default"])("rust", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_rust" */"refractor/lang/rust.js"));
    });
  }),
  sas: (0, _createLanguageAsyncLoader["default"])("sas", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sas" */"refractor/lang/sas.js"));
    });
  }),
  sass: (0, _createLanguageAsyncLoader["default"])("sass", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sass" */"refractor/lang/sass.js"));
    });
  }),
  scala: (0, _createLanguageAsyncLoader["default"])("scala", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_scala" */"refractor/lang/scala.js"));
    });
  }),
  scheme: (0, _createLanguageAsyncLoader["default"])("scheme", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_scheme" */"refractor/lang/scheme.js"));
    });
  }),
  scss: (0, _createLanguageAsyncLoader["default"])("scss", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_scss" */"refractor/lang/scss.js"));
    });
  }),
  shellSession: (0, _createLanguageAsyncLoader["default"])("shellSession", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_shellSession" */"refractor/lang/shell-session.js"));
    });
  }),
  smali: (0, _createLanguageAsyncLoader["default"])("smali", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_smali" */"refractor/lang/smali.js"));
    });
  }),
  smalltalk: (0, _createLanguageAsyncLoader["default"])("smalltalk", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_smalltalk" */"refractor/lang/smalltalk.js"));
    });
  }),
  smarty: (0, _createLanguageAsyncLoader["default"])("smarty", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_smarty" */"refractor/lang/smarty.js"));
    });
  }),
  sml: (0, _createLanguageAsyncLoader["default"])("sml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sml" */"refractor/lang/sml.js"));
    });
  }),
  solidity: (0, _createLanguageAsyncLoader["default"])("solidity", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_solidity" */"refractor/lang/solidity.js"));
    });
  }),
  solutionFile: (0, _createLanguageAsyncLoader["default"])("solutionFile", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_solutionFile" */"refractor/lang/solution-file.js"));
    });
  }),
  soy: (0, _createLanguageAsyncLoader["default"])("soy", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_soy" */"refractor/lang/soy.js"));
    });
  }),
  sparql: (0, _createLanguageAsyncLoader["default"])("sparql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sparql" */"refractor/lang/sparql.js"));
    });
  }),
  splunkSpl: (0, _createLanguageAsyncLoader["default"])("splunkSpl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_splunkSpl" */"refractor/lang/splunk-spl.js"));
    });
  }),
  sqf: (0, _createLanguageAsyncLoader["default"])("sqf", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sqf" */"refractor/lang/sqf.js"));
    });
  }),
  sql: (0, _createLanguageAsyncLoader["default"])("sql", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_sql" */"refractor/lang/sql.js"));
    });
  }),
  squirrel: (0, _createLanguageAsyncLoader["default"])("squirrel", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_squirrel" */"refractor/lang/squirrel.js"));
    });
  }),
  stan: (0, _createLanguageAsyncLoader["default"])("stan", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_stan" */"refractor/lang/stan.js"));
    });
  }),
  stylus: (0, _createLanguageAsyncLoader["default"])("stylus", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_stylus" */"refractor/lang/stylus.js"));
    });
  }),
  swift: (0, _createLanguageAsyncLoader["default"])("swift", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_swift" */"refractor/lang/swift.js"));
    });
  }),
  systemd: (0, _createLanguageAsyncLoader["default"])("systemd", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_systemd" */"refractor/lang/systemd.js"));
    });
  }),
  t4Cs: (0, _createLanguageAsyncLoader["default"])("t4Cs", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_t4Cs" */"refractor/lang/t4-cs.js"));
    });
  }),
  t4Templating: (0, _createLanguageAsyncLoader["default"])("t4Templating", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_t4Templating" */"refractor/lang/t4-templating.js"));
    });
  }),
  t4Vb: (0, _createLanguageAsyncLoader["default"])("t4Vb", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_t4Vb" */"refractor/lang/t4-vb.js"));
    });
  }),
  tap: (0, _createLanguageAsyncLoader["default"])("tap", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_tap" */"refractor/lang/tap.js"));
    });
  }),
  tcl: (0, _createLanguageAsyncLoader["default"])("tcl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_tcl" */"refractor/lang/tcl.js"));
    });
  }),
  textile: (0, _createLanguageAsyncLoader["default"])("textile", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_textile" */"refractor/lang/textile.js"));
    });
  }),
  toml: (0, _createLanguageAsyncLoader["default"])("toml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_toml" */"refractor/lang/toml.js"));
    });
  }),
  tremor: (0, _createLanguageAsyncLoader["default"])("tremor", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_tremor" */"refractor/lang/tremor.js"));
    });
  }),
  tsx: (0, _createLanguageAsyncLoader["default"])("tsx", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_tsx" */"refractor/lang/tsx.js"));
    });
  }),
  tt2: (0, _createLanguageAsyncLoader["default"])("tt2", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_tt2" */"refractor/lang/tt2.js"));
    });
  }),
  turtle: (0, _createLanguageAsyncLoader["default"])("turtle", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_turtle" */"refractor/lang/turtle.js"));
    });
  }),
  twig: (0, _createLanguageAsyncLoader["default"])("twig", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_twig" */"refractor/lang/twig.js"));
    });
  }),
  typescript: (0, _createLanguageAsyncLoader["default"])("typescript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_typescript" */"refractor/lang/typescript.js"));
    });
  }),
  typoscript: (0, _createLanguageAsyncLoader["default"])("typoscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_typoscript" */"refractor/lang/typoscript.js"));
    });
  }),
  unrealscript: (0, _createLanguageAsyncLoader["default"])("unrealscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_unrealscript" */"refractor/lang/unrealscript.js"));
    });
  }),
  uorazor: (0, _createLanguageAsyncLoader["default"])("uorazor", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_uorazor" */"refractor/lang/uorazor.js"));
    });
  }),
  uri: (0, _createLanguageAsyncLoader["default"])("uri", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_uri" */"refractor/lang/uri.js"));
    });
  }),
  v: (0, _createLanguageAsyncLoader["default"])("v", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_v" */"refractor/lang/v.js"));
    });
  }),
  vala: (0, _createLanguageAsyncLoader["default"])("vala", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_vala" */"refractor/lang/vala.js"));
    });
  }),
  vbnet: (0, _createLanguageAsyncLoader["default"])("vbnet", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_vbnet" */"refractor/lang/vbnet.js"));
    });
  }),
  velocity: (0, _createLanguageAsyncLoader["default"])("velocity", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_velocity" */"refractor/lang/velocity.js"));
    });
  }),
  verilog: (0, _createLanguageAsyncLoader["default"])("verilog", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_verilog" */"refractor/lang/verilog.js"));
    });
  }),
  vhdl: (0, _createLanguageAsyncLoader["default"])("vhdl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_vhdl" */"refractor/lang/vhdl.js"));
    });
  }),
  vim: (0, _createLanguageAsyncLoader["default"])("vim", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_vim" */"refractor/lang/vim.js"));
    });
  }),
  visualBasic: (0, _createLanguageAsyncLoader["default"])("visualBasic", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_visualBasic" */"refractor/lang/visual-basic.js"));
    });
  }),
  warpscript: (0, _createLanguageAsyncLoader["default"])("warpscript", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_warpscript" */"refractor/lang/warpscript.js"));
    });
  }),
  wasm: (0, _createLanguageAsyncLoader["default"])("wasm", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_wasm" */"refractor/lang/wasm.js"));
    });
  }),
  webIdl: (0, _createLanguageAsyncLoader["default"])("webIdl", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_webIdl" */"refractor/lang/web-idl.js"));
    });
  }),
  wiki: (0, _createLanguageAsyncLoader["default"])("wiki", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_wiki" */"refractor/lang/wiki.js"));
    });
  }),
  wolfram: (0, _createLanguageAsyncLoader["default"])("wolfram", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_wolfram" */"refractor/lang/wolfram.js"));
    });
  }),
  wren: (0, _createLanguageAsyncLoader["default"])("wren", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_wren" */"refractor/lang/wren.js"));
    });
  }),
  xeora: (0, _createLanguageAsyncLoader["default"])("xeora", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_xeora" */"refractor/lang/xeora.js"));
    });
  }),
  xmlDoc: (0, _createLanguageAsyncLoader["default"])("xmlDoc", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_xmlDoc" */"refractor/lang/xml-doc.js"));
    });
  }),
  xojo: (0, _createLanguageAsyncLoader["default"])("xojo", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_xojo" */"refractor/lang/xojo.js"));
    });
  }),
  xquery: (0, _createLanguageAsyncLoader["default"])("xquery", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_xquery" */"refractor/lang/xquery.js"));
    });
  }),
  yaml: (0, _createLanguageAsyncLoader["default"])("yaml", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_yaml" */"refractor/lang/yaml.js"));
    });
  }),
  yang: (0, _createLanguageAsyncLoader["default"])("yang", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_yang" */"refractor/lang/yang.js"));
    });
  }),
  zig: (0, _createLanguageAsyncLoader["default"])("zig", function () {
    return Promise.resolve().then(function () {
      return _interopRequireWildcard(require( /* webpackChunkName: "react-syntax-highlighter_languages_refractor_zig" */"refractor/lang/zig.js"));
    });
  })
};