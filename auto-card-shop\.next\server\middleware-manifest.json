{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2ebd57a8._.js", "server/edge/chunks/[root-of-the-server]__b1baa225._.js", "server/edge/chunks/edge-wrapper_27779927.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W1guVrC+Px7EIJSfR0SnSL5iOnRJMCzuf2lPvgcuNgA=", "__NEXT_PREVIEW_MODE_ID": "daa5eb728a4e004d816ac605fbf5d6cd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "884ec16e78977ee9aefcc9796eb3570bdf363713c041f8f24a0c2821206f22d5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "81ef9400e0d0b8fcd9b47fac9b48a479f3b25efdcc520b932083c1f229635cb1"}}}, "sortedMiddleware": ["/"], "functions": {}}