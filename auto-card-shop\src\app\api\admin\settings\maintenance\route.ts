import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { promises as fs } from 'fs'
import path from 'path'

const MAINTENANCE_FILE = path.join(process.cwd(), 'maintenance.json')

interface MaintenanceSettings {
  maintenanceMode: boolean
  maintenanceMessage: string
  updatedAt: string
}

// 读取维护设置
async function readMaintenanceSettings(): Promise<MaintenanceSettings> {
  try {
    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认设置
    return {
      maintenanceMode: false,
      maintenanceMessage: '网站正在维护中，请稍后再试。',
      updatedAt: new Date().toISOString()
    }
  }
}

// 写入维护设置
async function writeMaintenanceSettings(settings: MaintenanceSettings): Promise<void> {
  await fs.writeFile(MAINTENANCE_FILE, JSON.stringify(settings, null, 2), 'utf8')
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const settings = await readMaintenanceSettings()
    return NextResponse.json(settings)

  } catch (error) {
    console.error('获取维护设置失败:', error)
    return NextResponse.json({ error: '获取设置失败' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const { maintenanceMode, maintenanceMessage } = await request.json()

    // 验证输入
    if (typeof maintenanceMode !== 'boolean') {
      return NextResponse.json({ error: '维护模式状态必须是布尔值' }, { status: 400 })
    }

    if (!maintenanceMessage || typeof maintenanceMessage !== 'string') {
      return NextResponse.json({ error: '维护消息不能为空' }, { status: 400 })
    }

    const settings: MaintenanceSettings = {
      maintenanceMode,
      maintenanceMessage: maintenanceMessage.trim(),
      updatedAt: new Date().toISOString()
    }

    await writeMaintenanceSettings(settings)

    return NextResponse.json({ 
      success: true, 
      message: '维护设置保存成功',
      settings 
    })

  } catch (error) {
    console.error('保存维护设置失败:', error)
    return NextResponse.json({ error: '保存设置失败' }, { status: 500 })
  }
}
