import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { startOfDay, endOfDay, startOfMonth, endOfMonth, subDays, subMonths, format } from 'date-fns'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const now = new Date()
    const today = startOfDay(now)
    const endToday = endOfDay(now)
    const thisMonth = startOfMonth(now)
    const endThisMonth = endOfMonth(now)
    const lastMonth = startOfMonth(subMonths(now, 1))
    const endLastMonth = endOfMonth(subMonths(now, 1))

    // 获取今日销售数据
    const todayOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: today,
          lte: endToday
        },
        status: 'COMPLETED'
      }
    })

    // 获取本月销售数据
    const thisMonthOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: thisMonth,
          lte: endThisMonth
        },
        status: 'COMPLETED'
      }
    })

    // 获取上月销售数据
    const lastMonthOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: lastMonth,
          lte: endLastMonth
        },
        status: 'COMPLETED'
      }
    })

    // 获取最近7天的销售趋势
    const salesTrend = []
    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i)
      const dayStart = startOfDay(date)
      const dayEnd = endOfDay(date)
      
      const dayOrders = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: dayStart,
            lte: dayEnd
          },
          status: 'COMPLETED'
        }
      })

      salesTrend.push({
        date: format(date, 'MM-dd'),
        sales: dayOrders.reduce((sum, order) => sum + order.totalAmount, 0),
        orders: dayOrders.length
      })
    }

    // 获取热门商品
    const popularProducts = await prisma.order.groupBy({
      by: ['productId'],
      where: {
        status: 'COMPLETED',
        createdAt: {
          gte: thisMonth
        }
      },
      _count: {
        id: true
      },
      _sum: {
        totalAmount: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 5
    })

    // 获取商品详细信息
    const popularProductsWithDetails = await Promise.all(
      popularProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true, image: true }
        })
        return {
          ...item,
          product
        }
      })
    )

    // 获取收入统计
    const revenueStats = await prisma.order.aggregate({
      where: {
        status: 'COMPLETED'
      },
      _sum: {
        totalAmount: true
      },
      _count: {
        id: true
      }
    })

    // 计算统计数据
    const todayRevenue = todayOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    const thisMonthRevenue = thisMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    
    const monthlyGrowth = lastMonthRevenue > 0 
      ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
      : 0

    // 获取用户增长数据
    const totalUsers = await prisma.user.count()
    const thisMonthUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thisMonth
        }
      }
    })

    // 获取商品库存警告
    const lowStockProducts = await prisma.product.findMany({
      where: {
        status: 'ACTIVE'
      },
      include: {
        _count: {
          select: {
            cards: {
              where: {
                status: 'AVAILABLE'
              }
            }
          }
        }
      }
    })

    const lowStockWarnings = lowStockProducts
      .filter(product => product._count.cards < 10)
      .map(product => ({
        id: product.id,
        name: product.name,
        stock: product._count.cards
      }))

    return NextResponse.json({
      todayRevenue,
      thisMonthRevenue,
      lastMonthRevenue,
      monthlyGrowth,
      todayOrders: todayOrders.length,
      thisMonthOrders: thisMonthOrders.length,
      totalRevenue: revenueStats._sum.totalAmount || 0,
      totalOrders: revenueStats._count || 0,
      totalUsers,
      thisMonthUsers,
      salesTrend,
      popularProducts: popularProductsWithDetails,
      lowStockWarnings
    })

  } catch (error) {
    console.error('获取统计数据失败:', error)
    return NextResponse.json({ error: '获取统计数据失败' }, { status: 500 })
  }
}
